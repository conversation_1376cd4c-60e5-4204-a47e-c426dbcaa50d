import json
from unittest.mock import patch, MagicMock
from django.urls import reverse
from django.test import TestCase, Client
from rest_framework import status

from ..models import Ekyc, Liveness
from ..models.liveness import STATUS_PENDING
from ..helpers.liveness_integration_flags import compute_liveness_integration_flags
from .utils import AppliedFormMock, FormMock


class LivenessIadTests(TestCase):
    """Test cases for LivenessIadViewSet, focusing on the pending API and compute_liveness_integration_flags"""

    def setUp(self):
        """Set up test data"""
        self.client = Client()
        self.ekyc_ref = "test_ref_123"

        # Mock applied form
        self.applied_form = AppliedFormMock()

        # Mock get_applied_form
        patcher_get_applied_form = patch("ekyc.views.liveness_iad.get_applied_form")
        self.mock_get_applied_form = patcher_get_applied_form.start()
        self.mock_get_applied_form.return_value = self.applied_form
        self.addCleanup(patcher_get_applied_form.stop)

        # Mock encrypt_header
        patcher_encrypt_header = patch("ekyc.views.liveness_iad.encrypt_header")
        self.mock_encrypt_header = patcher_encrypt_header.start()
        self.mock_encrypt_header.return_value = {"encrypted": "response"}
        self.addCleanup(patcher_encrypt_header.stop)

        # Mock call_webhook
        patcher_call_webhook = patch("ekyc.models.liveness.Liveness.call_webhook")
        self.mock_call_webhook = patcher_call_webhook.start()
        self.addCleanup(patcher_call_webhook.stop)

        # Mock add_applied_form_log
        patcher_add_log = patch("ekyc.views.liveness_iad.add_applied_form_log")
        self.mock_add_log = patcher_add_log.start()
        self.addCleanup(patcher_add_log.stop)

    def test_pending_api_success_with_iad_enabled(self):
        """Test pending API succeeds when IAD is enabled through integration flags"""
        # Mock compute_liveness_integration_flags to return enable_iad=True
        with patch("ekyc.views.liveness_iad.compute_liveness_integration_flags") as mock_compute:
            mock_compute.return_value = {"enable_iad": True}

            response = self.client.post(
                reverse("ekyc:liveness-iad-pending", kwargs={"ekyc_ref": self.ekyc_ref}),
                {"log": {"test": "data"}},
                content_type="application/json",
            )

            self.assertEqual(response.status_code, status.HTTP_200_OK)
            self.assertEqual(response.json(), {"encrypted": "response"})

            # Verify ekyc and liveness objects were created
            ekyc = Ekyc.objects.get(ref=self.ekyc_ref)
            self.assertTrue(ekyc.liveness_set.exists())

            liveness = ekyc.liveness_set.first()
            self.assertEqual(json.loads(liveness.log), {"test": "data"})

            # Verify webhook was called with STATUS_PENDING
            self.mock_call_webhook.assert_called_once_with(action_status=STATUS_PENDING)

            # Verify compute_liveness_integration_flags was called with applied_form
            mock_compute.assert_called_once_with(self.applied_form)

    def test_pending_api_forbidden_when_iad_disabled(self):
        """Test pending API returns 403 when IAD is disabled through integration flags"""
        # Mock compute_liveness_integration_flags to return enable_iad=False
        with patch("ekyc.views.liveness_iad.compute_liveness_integration_flags") as mock_compute:
            mock_compute.return_value = {"enable_iad": False}

            response = self.client.post(
                reverse("ekyc:liveness-iad-pending", kwargs={"ekyc_ref": self.ekyc_ref}),
                {"log": {"test": "data"}},
                content_type="application/json",
            )

            self.assertEqual(response.status_code, status.HTTP_403_FORBIDDEN)

            # Verify no liveness object was created
            ekyc = Ekyc.objects.get(ref=self.ekyc_ref)
            self.assertFalse(ekyc.liveness_set.exists())

            # Verify compute_liveness_integration_flags was called
            mock_compute.assert_called_once_with(self.applied_form)

    def test_pending_api_forbidden_when_no_iad_flag(self):
        """Test pending API returns 403 when integration flags don't include enable_iad"""
        # Mock compute_liveness_integration_flags to return empty dict
        with patch("ekyc.views.liveness_iad.compute_liveness_integration_flags") as mock_compute:
            mock_compute.return_value = {}

            response = self.client.post(
                reverse("ekyc:liveness-iad-pending", kwargs={"ekyc_ref": self.ekyc_ref}),
                {"log": {"test": "data"}},
                content_type="application/json",
            )

            self.assertEqual(response.status_code, status.HTTP_403_FORBIDDEN)

    def test_pending_api_max_attempt_exceeded(self):
        """Test pending API returns 400 when liveness max attempt is exceeded"""
        # Create ekyc with no attempts left
        ekyc = Ekyc.objects.create(ref=self.ekyc_ref)

        with patch.object(ekyc, "liveness_has_attempt", False):
            with patch("ekyc.views.liveness_iad.compute_liveness_integration_flags") as mock_compute:
                mock_compute.return_value = {"enable_iad": True}

                response = self.client.post(
                    reverse("ekyc:liveness-iad-pending", kwargs={"ekyc_ref": self.ekyc_ref}),
                    {"log": {"test": "data"}},
                    content_type="application/json",
                )

                self.assertEqual(response.status_code, status.HTTP_400_BAD_REQUEST)

    def test_pending_api_invalid_serializer_data(self):
        """Test pending API returns 422 when serializer validation fails"""
        # FaceActionSerializer expects log to be a dict, so pass invalid data
        response = self.client.post(
            reverse("ekyc:liveness-iad-pending", kwargs={"ekyc_ref": self.ekyc_ref}),
            {"log": "invalid_string_instead_of_dict"},
            content_type="application/json",
        )

        self.assertEqual(response.status_code, status.HTTP_422_UNPROCESSABLE_ENTITY)


class TestComputeLivenessIntegrationFlags(TestCase):
    """Test cases specifically for compute_liveness_integration_flags function"""

    def setUp(self):
        """Set up test data"""
        self.applied_form = MagicMock()
        self.applied_form.answer_set.filter.return_value = []

    def test_no_integrations_config_returns_empty_dict(self):
        """Test that missing integrations config returns empty dict"""
        with patch("ekyc.helpers.liveness_integration_flags.get_form_settings") as mock_get_settings:
            mock_get_settings.return_value = {}

            result = compute_liveness_integration_flags(self.applied_form)

            self.assertEqual(result, {})
            mock_get_settings.assert_called_once_with(self.applied_form, "ekyc.liveness.integrations", {})

    def test_integrations_disabled_returns_empty_dict(self):
        """Test that disabled integrations returns empty dict"""
        with patch("ekyc.helpers.liveness_integration_flags.get_form_settings") as mock_get_settings:
            mock_get_settings.return_value = {"enabled": False}

            result = compute_liveness_integration_flags(self.applied_form)

            self.assertEqual(result, {})

    def test_no_conditions_returns_default_flags(self):
        """Test that enabled integrations with no conditions returns default flags"""
        with patch("ekyc.helpers.liveness_integration_flags.get_form_settings") as mock_get_settings:
            mock_get_settings.return_value = {"enabled": True, "conditions": []}

            result = compute_liveness_integration_flags(self.applied_form)

            self.assertEqual(result, {"enable_iad": True})

    def test_condition_passes_returns_flags(self):
        """Test that passing conditions return the associated flags"""
        integrations_config = {
            "enabled": True,
            "conditions": [
                {
                    "name": "test_condition",
                    "conditions": [
                        {
                            "category": "answer",
                            "value": "test_question",
                            "operator": "equals",
                            "expected": "test_value",
                        }
                    ],
                    "flags": {"enable_iad": True, "custom_flag": "test"},
                }
            ],
        }

        # Mock answer for the condition
        mock_answer = MagicMock()
        mock_answer.question = "test_question"
        mock_answer.value = "test_value"
        self.applied_form.answer_set.filter.return_value = [mock_answer]

        with patch("ekyc.helpers.liveness_integration_flags.get_form_settings") as mock_get_settings:
            mock_get_settings.return_value = integrations_config

            with patch("decision_flow.entity.node_condition.NodeCondition") as mock_node_condition:
                mock_instance = mock_node_condition.return_value
                mock_instance.do_compute.return_value = True

                result = compute_liveness_integration_flags(self.applied_form)

                self.assertEqual(result, {"enable_iad": True, "custom_flag": "test"})

    def test_condition_fails_returns_empty_flags(self):
        """Test that failing conditions return empty flags"""
        integrations_config = {
            "enabled": True,
            "conditions": [
                {
                    "name": "test_condition",
                    "conditions": [
                        {
                            "category": "answer",
                            "value": "test_question",
                            "operator": "equals",
                            "expected": "test_value",
                        }
                    ],
                    "flags": {"enable_iad": True},
                }
            ],
        }

        with patch("ekyc.helpers.liveness_integration_flags.get_form_settings") as mock_get_settings:
            mock_get_settings.return_value = integrations_config

            with patch("decision_flow.entity.node_condition.NodeCondition") as mock_node_condition:
                mock_instance = mock_node_condition.return_value
                mock_instance.do_compute.return_value = False

                result = compute_liveness_integration_flags(self.applied_form)

                self.assertEqual(result, {})

    def test_multiple_conditions_merge_flags(self):
        """Test that multiple passing conditions merge their flags"""
        integrations_config = {
            "enabled": True,
            "conditions": [
                {
                    "name": "condition1",
                    "conditions": [],  # No conditions means always pass
                    "flags": {"enable_iad": True, "flag1": "value1"},
                },
                {
                    "name": "condition2",
                    "conditions": [],  # No conditions means always pass
                    "flags": {"flag2": "value2", "flag3": "value3"},
                },
            ],
        }

        with patch("ekyc.helpers.liveness_integration_flags.get_form_settings") as mock_get_settings:
            mock_get_settings.return_value = integrations_config

            result = compute_liveness_integration_flags(self.applied_form)

            expected = {"enable_iad": True, "flag1": "value1", "flag2": "value2", "flag3": "value3"}
            self.assertEqual(result, expected)

    def test_crawl_answers_modifies_conditions(self):
        """Test that crawl_answers function properly modifies condition objects"""
        integrations_config = {
            "enabled": True,
            "conditions": [
                {
                    "name": "test_condition",
                    "conditions": [
                        {
                            "category": "answer",
                            "value": "test_question",
                            "operator": "equals",
                            "expected": "test_value",
                        }
                    ],
                    "flags": {"enable_iad": True},
                }
            ],
        }

        # Mock answer for the condition
        mock_answer = MagicMock()
        mock_answer.question = "test_question"
        mock_answer.value = "test_value"
        self.applied_form.answer_set.filter.return_value = [mock_answer]

        with patch("ekyc.helpers.liveness_integration_flags.get_form_settings") as mock_get_settings:
            mock_get_settings.return_value = integrations_config

            with patch("decision_flow.entity.node_condition.NodeCondition") as mock_node_condition:
                mock_instance = mock_node_condition.return_value
                mock_instance.do_compute.return_value = True

                compute_liveness_integration_flags(self.applied_form)

                # Verify that NodeCondition was called with proper context
                mock_node_condition.assert_called_once()
                call_args = mock_node_condition.call_args

                # Check that context contains the answer data
                context = call_args[1]["context"]
                self.assertIn("data_point_result", context)
                self.assertIn("answer", context["data_point_result"])
                self.assertEqual(context["data_point_result"]["answer"]["test_question"], "test_value")


class LivenessIadOtherEndpointsTests(TestCase):
    """Test cases for other endpoints in LivenessIadViewSet (start, cancel, fail, submit, log)"""

    def setUp(self):
        """Set up test data"""
        self.client = Client()
        self.ekyc_ref = "test_ref_123"

    def test_start_endpoint_returns_ok(self):
        """Test start endpoint returns OK message"""
        response = self.client.post(reverse("ekyc:liveness-iad-start", kwargs={"ekyc_ref": self.ekyc_ref}))

        self.assertEqual(response.status_code, status.HTTP_200_OK)
        self.assertEqual(response.json(), {"message": "OK"})

    def test_cancel_endpoint_returns_ok(self):
        """Test cancel endpoint returns OK message"""
        response = self.client.post(reverse("ekyc:liveness-iad-cancel", kwargs={"ekyc_ref": self.ekyc_ref}))

        self.assertEqual(response.status_code, status.HTTP_200_OK)
        self.assertEqual(response.json(), {"message": "OK"})

    def test_fail_endpoint_returns_ok(self):
        """Test fail endpoint returns OK message"""
        response = self.client.post(reverse("ekyc:liveness-iad-fail", kwargs={"ekyc_ref": self.ekyc_ref}))

        self.assertEqual(response.status_code, status.HTTP_200_OK)
        self.assertEqual(response.json(), {"message": "OK"})

    def test_submit_endpoint_returns_ok(self):
        """Test submit endpoint returns OK message"""
        response = self.client.post(reverse("ekyc:liveness-iad-submit", kwargs={"ekyc_ref": self.ekyc_ref}))

        self.assertEqual(response.status_code, status.HTTP_200_OK)
        self.assertEqual(response.json(), {"message": "OK"})

    def test_log_endpoint_returns_ok(self):
        """Test log endpoint returns OK message"""
        response = self.client.post(reverse("ekyc:liveness-iad-log", kwargs={"ekyc_ref": self.ekyc_ref}))

        self.assertEqual(response.status_code, status.HTTP_200_OK)
        self.assertEqual(response.json(), {"message": "OK"})


class LivenessIadIntegrationTests(TestCase):
    """Integration tests for LivenessIadViewSet with real-world scenarios"""

    def setUp(self):
        """Set up test data"""
        self.client = Client()
        self.ekyc_ref = "test_ref_123"

        # Create a real applied form mock with form settings
        self.form = FormMock()
        self.applied_form = AppliedFormMock(form=self.form)

        # Mock get_applied_form
        patcher_get_applied_form = patch("ekyc.views.liveness_iad.get_applied_form")
        self.mock_get_applied_form = patcher_get_applied_form.start()
        self.mock_get_applied_form.return_value = self.applied_form
        self.addCleanup(patcher_get_applied_form.stop)

        # Mock encrypt_header
        patcher_encrypt_header = patch("ekyc.views.liveness_iad.encrypt_header")
        self.mock_encrypt_header = patcher_encrypt_header.start()
        self.mock_encrypt_header.return_value = {"encrypted": "response"}
        self.addCleanup(patcher_encrypt_header.stop)

        # Mock call_webhook
        patcher_call_webhook = patch("ekyc.models.liveness.Liveness.call_webhook")
        self.mock_call_webhook = patcher_call_webhook.start()
        self.addCleanup(patcher_call_webhook.stop)

        # Mock add_applied_form_log
        patcher_add_log = patch("ekyc.views.liveness_iad.add_applied_form_log")
        self.mock_add_log = patcher_add_log.start()
        self.addCleanup(patcher_add_log.stop)

    def test_pending_api_with_form_settings_iad_enabled(self):
        """Test pending API with form settings that enable IAD"""
        # Configure form settings to enable IAD
        self.form.setting["ekyc"]["liveness"]["integrations"] = {
            "enabled": True,
            "conditions": [],  # Empty conditions should return default flags
        }

        response = self.client.post(
            reverse("ekyc:liveness-iad-pending", kwargs={"ekyc_ref": self.ekyc_ref}),
            {"log": {"integration_test": "data"}},
            content_type="application/json",
        )

        self.assertEqual(response.status_code, status.HTTP_200_OK)

        # Verify liveness object was created with correct data
        ekyc = Ekyc.objects.get(ref=self.ekyc_ref)
        liveness = ekyc.liveness_set.first()
        self.assertIsNotNone(liveness)
        self.assertEqual(json.loads(liveness.log), {"integration_test": "data"})

        # Verify face_actions is properly set
        face_actions = json.loads(liveness.face_actions)
        self.assertIsInstance(face_actions, list)

        # Verify session handling
        self.assertIsNotNone(self.client.session.get("liveness_id"))
        self.assertEqual(self.client.session["liveness_id"], liveness.id)

    def test_pending_api_with_form_settings_iad_disabled(self):
        """Test pending API with form settings that disable IAD"""
        # Configure form settings to disable IAD
        self.form.setting["ekyc"]["liveness"]["integrations"] = {"enabled": False}

        response = self.client.post(
            reverse("ekyc:liveness-iad-pending", kwargs={"ekyc_ref": self.ekyc_ref}),
            {"log": {"integration_test": "data"}},
            content_type="application/json",
        )

        self.assertEqual(response.status_code, status.HTTP_403_FORBIDDEN)

        # Verify no liveness object was created
        ekyc = Ekyc.objects.get(ref=self.ekyc_ref)
        self.assertFalse(ekyc.liveness_set.exists())

    def test_pending_api_exception_handling_in_get_applied_form(self):
        """Test pending API handles exceptions in get_applied_form gracefully"""
        # Mock get_applied_form to raise an exception
        self.mock_get_applied_form.side_effect = Exception("Database error")

        # Mock compute_liveness_integration_flags to return enable_iad=True
        with patch("ekyc.views.liveness_iad.compute_liveness_integration_flags") as mock_compute:
            mock_compute.return_value = {"enable_iad": True}

            response = self.client.post(
                reverse("ekyc:liveness-iad-pending", kwargs={"ekyc_ref": self.ekyc_ref}),
                {"log": {"test": "data"}},
                content_type="application/json",
            )

            # The API should still work because the exception is caught and printed
            self.assertEqual(response.status_code, status.HTTP_200_OK)

    def test_pending_api_url_generation(self):
        """Test that pending API generates correct URLs in response"""
        # Mock compute_liveness_integration_flags to return enable_iad=True
        with patch("ekyc.views.liveness_iad.compute_liveness_integration_flags") as mock_compute:
            mock_compute.return_value = {"enable_iad": True}

            # Mock encrypt_header to return the actual data for verification
            def mock_encrypt_side_effect(data, key):
                return json.loads(data)

            self.mock_encrypt_header.side_effect = mock_encrypt_side_effect

            response = self.client.post(
                reverse("ekyc:liveness-iad-pending", kwargs={"ekyc_ref": self.ekyc_ref}),
                {"log": {"test": "data"}},
                content_type="application/json",
            )

            self.assertEqual(response.status_code, status.HTTP_200_OK)
            response_data = response.json()

            # Verify all required URLs are present
            self.assertIn("iad_upload_url", response_data)
            self.assertIn("iad_log_url", response_data)
            self.assertIn("iad_start_url", response_data)
            self.assertIn("iad_cancel_url", response_data)
            self.assertIn("iad_fail_url", response_data)

            # Verify URLs contain the correct ekyc_ref
            for url_key, url_value in response_data.items():
                if url_key.endswith("_url"):
                    self.assertIn(self.ekyc_ref, url_value)
