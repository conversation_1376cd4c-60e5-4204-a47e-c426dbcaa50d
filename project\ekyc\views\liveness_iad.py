import json
import time
from datetime import datetime
from typing import Tuple

from django.conf import settings
from django.core.files.base import ContentFile
from django.shortcuts import get_object_or_404
from pydash import get
from rest_framework import status
from rest_framework.decorators import action
from rest_framework.parsers import <PERSON><PERSON><PERSON><PERSON><PERSON>, MultiPartParser
from rest_framework.response import Response
from rest_framework.reverse import reverse
from rest_framework.views import Request

from known_face.known_face import KnownFaceApp
from project.custom_logger import logger

from ..helpers.liveness_integration_flags import compute_liveness_integration_flags
from ..helpers.credit import raise_if_cant_deduct_credit_liveness
from ..helpers.external_api import upload_liveness
from ..helpers.faceactions import FaceActions
from ..helpers.file_hash import get_file_hash
from ..helpers.get_form import (
    get_applied_form,
    get_ekyc_current_attempt,
    get_form_answer,
    get_form_answers,
    get_form_applied_form_log_from_action,
    get_form_liveness_item,
    get_form_settings,
)
from ..helpers.log import add_applied_form_log, send_log
from ..helpers.set_transaction import do_ensure_facecompare, set_liveness
from ..helpers.sjcl_encryption import decrypt_files, decrypt_header
from ..helpers.url_encryption import encrypt_header
from ..helpers.utils import get_additional_header
from ..models import Ekyc, Liveness, LivenessLog
from ..models.liveness import (
    STATUS_CANCELLED,
    STATUS_FAILED_BACKEND,
    STATUS_FAILED_FRONTEND,
    STATUS_PENDING,
    STATUS_STARTED,
)
from ..serializers import (
    EncryptedSerializer,
    FaceActionSerializer,
    LivenessActionSerializer,
    LivenessSubmitSerializer,
)
from .base_ekyc import BaseEkycViewset

ENABLE_SAVE_LIVENESS_LOG = settings.ENABLE_SAVE_LIVENESS_LOG
ENABLE_HITL = settings.ENABLE_HITL


def get_additional_body(form, applied_form, process_logs: list = None):
    if process_logs is None:
        process_logs = []

    # Get features
    process_logs.append(["get_body_features", str(datetime.now().isoformat())])
    features = get_form_settings(form, "ekyc.liveness.features", {})

    # Get additional_body from formsettings
    process_logs.append(["get_body_settings", str(datetime.now().isoformat())])
    additional_body_settings: dict[str, str] = get_form_settings(form, "ekyc.liveness.additional_body", {})

    # Get all answers_to_upload
    process_logs.append(["get_body_answers", str(datetime.now().isoformat())])
    answers_to_upload_map: dict[str, str] = get_form_settings(form, "ekyc.liveness.answers_to_upload", {})
    answers_to_upload = {}
    if answers_to_upload_map and applied_form:
        for dest, src in answers_to_upload_map.items():
            answer = get_form_answer(applied_form, src)
            if not isinstance(answer, str):
                answer = json.dumps(answer)
            answers_to_upload[dest] = answer

    # Get face_compare_score_criteria
    additional_criteria = {}
    try:
        from dynamicform.hook import get_face_compare_criteria

        send_criteria = get_form_settings(form, "ekyc.liveness.send_face_compare_score_criteria", False)
        if send_criteria:
            additional_criteria["face_compare_score_criteria"] = get_face_compare_criteria(
                applied_form, "webhook_extra"
            )

    except Exception:
        pass

    return {
        "features": json.dumps(features),
        **additional_body_settings,
        **answers_to_upload,
        **additional_criteria,
    }


class LivenessIadViewSet(BaseEkycViewset):
    basename = "liveness-iad"
    parent_lookup_kwargs = {"ekyc": "ekyc"}
    parser_classes = (
        MultiPartParser,
        JSONParser,
    )

    model_name = "liveness"
    log_name = "liveness"

    @action(detail=False, methods=["post"])
    def pending(self, request, ekyc_ref, *args, **kwargs):
        serializer = FaceActionSerializer(data=request.data)
        if not serializer.is_valid():
            return Response(serializer.errors, status=status.HTTP_422_UNPROCESSABLE_ENTITY)

        ekyc, _ = Ekyc.objects.get_or_create(ref=ekyc_ref)
        if not ekyc.liveness_has_attempt:
            return Response(
                self.build_response(ekyc=ekyc, error_type="max_attempt"),
                status=status.HTTP_400_BAD_REQUEST,
            )

        try:
            applied_form = get_applied_form(ekyc.ref)
        except Exception as err:
            print(err)

        # Validate if this slug (ref) should be able to call this api
        integration_flags = compute_liveness_integration_flags(applied_form)
        should_include_iad = integration_flags.get("enable_iad", False)
        if not should_include_iad:
            return Response(
                self.build_response(ekyc=ekyc, error_type="not_allowed"),
                status=status.HTTP_403_FORBIDDEN,
            )

        faceactions = FaceActions([])
        actions = faceactions.actions
        if not request.session.session_key:
            request.session.create()
        liveness: Liveness = ekyc.liveness_set.create(
            face_actions=json.dumps(actions),
            face_actions_expire_at=faceactions.expireAt,
            action_sequence=json.dumps([]),
            log=json.dumps(serializer.data.get("log")),
        )

        add_applied_form_log(applied_form=applied_form, model_name=self.model_name, ekyc_item_obj=liveness)
        request.session["liveness_id"] = liveness.id

        build_url = lambda name: reverse(name, kwargs={"ekyc_ref": ekyc_ref}, request=request) + f"?ref={liveness.id}"
        result_response = {
            "iad_upload_url": build_url("ekyc:liveness-iad-submit"),
            "iad_log_url": build_url("ekyc:liveness-iad-log"),
            "iad_start_url": build_url("ekyc:liveness-iad-start"),
            "iad_cancel_url": build_url("ekyc:liveness-iad-cancel"),
            "iad_fail_url": build_url("ekyc:liveness-iad-fail"),
        }
        result_response_encrypted = encrypt_header(json.dumps(result_response), request.session.session_key)
        liveness.call_webhook(action_status=STATUS_PENDING)
        return Response(result_response_encrypted, status=status.HTTP_200_OK)

    @action(detail=False, methods=["post"])
    def start(self, request, *args, **kwargs):
        return Response({"message": "OK"}, status=status.HTTP_200_OK)

    @action(detail=False, methods=["post"])
    def cancel(self, request, *args, **kwargs):
        return Response({"message": "OK"}, status=status.HTTP_200_OK)

    @action(detail=False, methods=["post"])
    def fail(self, request, *args, **kwargs):
        return Response({"message": "OK"}, status=status.HTTP_200_OK)

    @action(detail=False, methods=["post"])
    def submit(self, request, *args, **kwargs):
        return Response({"message": "OK"}, status=status.HTTP_200_OK)

    @action(detail=False, methods=["post"])
    def log(self, request, *args, **kwargs):
        return Response({"message": "OK"}, status=status.HTTP_200_OK)
